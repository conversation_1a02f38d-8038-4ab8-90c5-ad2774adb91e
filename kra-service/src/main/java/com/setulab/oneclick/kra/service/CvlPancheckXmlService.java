package com.setulab.oneclick.kra.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.setulab.oneclick.kra.clients.CvlPancheckXmlServiceClientImpl;
import com.setulab.oneclick.kra.dto.cvl.pancheck.GetPanStatusRQ;
import com.setulab.oneclick.kra.enums.RandomNumberGenerationMethod;
import com.setulab.oneclick.kra.utils.RandomNumberUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class CvlPancheckXmlService {

    private final CvlPancheckXmlServiceClientImpl cvlPancheckXmlServiceClientImpl;

    public JsonNode getToken(String passKey) {
        try {
            log.info("Initiating GetToken for passKey: {}", passKey);
            if (passKey == null || passKey.isEmpty()) {
                passKey = RandomNumberUtils.generateRequestId(RandomNumberGenerationMethod.UUID);
                log.info("PassKey is not provided! Generated new passKey: {}", passKey);
            }

            // Call Xml Client
            JsonNode token = cvlPancheckXmlServiceClientImpl.GetToken(passKey);
            log.info("Token Response: {}", token);
            return token;
        } catch (Exception e) {
            return null;
        }
    }

    public JsonNode getPanStatus(GetPanStatusRQ getPanStatusRQ) {
        try {
            String token = getPanStatusRQ.getToken();
            String panNumber = getPanStatusRQ.getPanNumber();
            log.info("Initiating GetPanStatus Xml for panNumber: {}", panNumber);
            if (token == null || token.isEmpty()) {
                String passKey = RandomNumberUtils.generateRequestId(RandomNumberGenerationMethod.UUID);
                log.info("Generated new passKey: {}", passKey);
                token = cvlPancheckXmlServiceClientImpl.GetToken(passKey).get("token").asText();
                log.info("Token is not provided! Generated new token: {}", token);
            }

            // Call Xml Client
            JsonNode panStatus = cvlPancheckXmlServiceClientImpl.GetPanStatus(panNumber);
            log.info("Pan Status Response: {}", panStatus);

            return panStatus;
        } catch (Exception e) {
            return null;
        }
    }

    public JsonNode solicitPANDetailsFetchALLKRA(JsonNode jsonValue) {
        return null;
    }

    public JsonNode insertUpdateKYCRecord(JsonNode jsonValue) {
        return null;
    }
}
