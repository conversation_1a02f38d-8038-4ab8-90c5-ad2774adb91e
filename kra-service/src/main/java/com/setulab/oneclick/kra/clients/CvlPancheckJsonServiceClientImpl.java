package com.setulab.oneclick.kra.clients;

import com.fasterxml.jackson.databind.JsonNode;
import com.setulab.oneclick.kra.ApplicationProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Service("cvlPancheckJsonServiceClient")
public class CvlPancheckJsonServiceClientImpl implements CvlPancheckJsonServiceClient {

    private final RestTemplate restTemplate;
    private final ApplicationProperties applicationProperties;

    public CvlPancheckJsonServiceClientImpl(@Qualifier("cvlPancheckJsonRestTemplate") RestTemplate restTemplate, ApplicationProperties applicationProperties) {
        this.restTemplate = restTemplate;
        this.applicationProperties = applicationProperties;
    }

    @Override
    public JsonNode GetToken(String passKey) {
        String url = applicationProperties.getCvlPancheckJsonBaseUrl() + "/GetToken";
        log.info("Initiating GetToken Json (RestTemplate) for URl: {}", url);

        return null;
    }

    @Override
    public JsonNode GetPanStatus(JsonNode payload) {
        String url = applicationProperties.getCvlPancheckJsonBaseUrl() + "/GetPanStatus";
        log.info("Initiating GetPanStatus Json (RestTemplate) for URl: {}", url);
        return null;
    }

    @Override
    public JsonNode SolicitPANDetailsFetchALLKRA(JsonNode payload) {
        String url = applicationProperties.getCvlPancheckJsonBaseUrl() + "/SolicitPANDetailsFetchALLKRA";
        log.info("Initiating SolicitPANDetailsFetchALLKRA Json (RestTemplate) for URl: {}", url);
        return null;
    }

    @Override
    public JsonNode InsertUpdateKYCRecord(JsonNode payload) {
        String url = applicationProperties.getCvlPancheckJsonBaseUrl() + "/InsertUpdateKYCRecord";
        log.info("Initiating InsertUpdateKYCRecord Json (RestTemplate) for URl: {}", url);
        return null;
    }
}
