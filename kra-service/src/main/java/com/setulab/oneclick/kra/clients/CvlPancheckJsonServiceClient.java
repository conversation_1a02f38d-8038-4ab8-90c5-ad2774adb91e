package com.setulab.oneclick.kra.clients;

import com.fasterxml.jackson.databind.JsonNode;

/**
 * Client interface for Kra Json Service API v2.4
 * Based on Cvl Pancheck Json API documentation
 */
public interface CvlPancheckJsonServiceClient {

    /**
     * Get Token API - Get Token for further requests
     *
     * @param passKey Request json payload
     * @return String containing the Token response
     */
    JsonNode GetToken(String passKey);

    /**
     * Get PAN Status API - Get PAN Status for further requests
     *
     * @param payload Request json payload
     * @return String containing the PAN Status response
     */
    JsonNode GetPanStatus(JsonNode payload);

    /**
     * Solicit PAN Details Fetch ALL KRA API - Solicit PAN Details Fetch ALL KRA for further requests
     *
     * @param payload Request json payload
     * @return String containing the Solicit PAN Details Fetch ALL KRA response
     */
    JsonNode SolicitPANDetailsFetchALLKRA(JsonNode payload);

    /**
     * Insert/Update KYC Record API - Insert/Update KYC Record for further requests
     *
     * @param payload Request json payload
     * @return String containing the Insert/Update KYC Record response
     */
    JsonNode InsertUpdateKYCRecord(JsonNode payload);

}
